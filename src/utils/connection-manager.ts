import { Logger } from './index'

// 连接状态枚举
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting'
}

// 请求接口
interface PendingRequest {
  resolve: (value: any) => void
  reject: (error: Error) => void
  timeout: number
}

// 消息接口
interface ConnectionMessage {
  type: string
  data?: any
  requestId?: number
  error?: string
}

/**
 * Popup端连接管理器
 * 负责与background建立长连接并处理AI请求通信
 */
export class PopupConnectionManager {
  private static instance: PopupConnectionManager | null = null
  private port: chrome.runtime.Port | null = null
  private state: ConnectionState = ConnectionState.DISCONNECTED
  private requestId = 0
  private pendingRequests = new Map<number, PendingRequest>()
  private messageHandlers = new Map<string, (data: any) => void>()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private connectionPromise: Promise<void> | null = null

  private constructor() {}

  static getInstance(): PopupConnectionManager {
    if (!PopupConnectionManager.instance) {
      PopupConnectionManager.instance = new PopupConnectionManager()
    }
    return PopupConnectionManager.instance
  }

  /**
   * 建立连接
   */
  async connect(): Promise<void> {
    if (this.state === ConnectionState.CONNECTED) {
      return Promise.resolve()
    }

    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = this._connect()
    return this.connectionPromise
  }

  private async _connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.state = ConnectionState.CONNECTING
        Logger.info('[Connection] Establishing connection to background...')

        this.port = chrome.runtime.connect({ name: 'popup-ai-connection' })

        this.port.onMessage.addListener((message: ConnectionMessage) => {
          this.handleMessage(message)
        })

        this.port.onDisconnect.addListener(() => {
          Logger.warn('[Connection] Connection disconnected')
          this.handleDisconnect()
        })

        // 发送连接确认消息
        this.port.postMessage({
          type: 'connection-established',
          timestamp: Date.now()
        })

        this.state = ConnectionState.CONNECTED
        this.reconnectAttempts = 0
        this.connectionPromise = null
        Logger.success('[Connection] Successfully connected to background')
        resolve()

      } catch (error) {
        this.state = ConnectionState.DISCONNECTED
        this.connectionPromise = null
        Logger.error('[Connection] Failed to establish connection:', error)
        reject(error)
      }
    })
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: ConnectionMessage): void {
    Logger.debug('[Connection] Received message:', message)

    // 处理请求响应
    if (message.requestId && this.pendingRequests.has(message.requestId)) {
      const request = this.pendingRequests.get(message.requestId)!
      this.pendingRequests.delete(message.requestId)
      
      // 清除超时定时器
      clearTimeout(request.timeout)

      if (message.error) {
        request.reject(new Error(message.error))
      } else {
        request.resolve(message.data)
      }
      return
    }

    // 处理主动推送的消息
    const handler = this.messageHandlers.get(message.type)
    if (handler) {
      handler(message.data)
    }
  }

  /**
   * 处理连接断开
   */
  private handleDisconnect(): void {
    this.state = ConnectionState.DISCONNECTED
    this.port = null

    // 拒绝所有待处理的请求
    this.pendingRequests.forEach((request) => {
      clearTimeout(request.timeout)
      request.reject(new Error('Connection lost'))
    })
    this.pendingRequests.clear()

    // 尝试重连
    this.attemptReconnect()
  }

  /**
   * 尝试重连
   */
  private async attemptReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      Logger.error('[Connection] Max reconnection attempts reached')
      return
    }

    this.state = ConnectionState.RECONNECTING
    this.reconnectAttempts++
    
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    Logger.info(`[Connection] Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        Logger.error('[Connection] Reconnection failed:', error)
        this.attemptReconnect()
      }
    }, delay)
  }

  /**
   * 发送AI请求
   */
  async sendAIRequest(options: {
    prompt: string
    mode: string
    projectId?: string
    language: string
    description: string
    formFields: any[]
  }): Promise<any> {
    await this.connect()

    if (!this.port || this.state !== ConnectionState.CONNECTED) {
      throw new Error('Connection not available')
    }

    const requestId = ++this.requestId
    Logger.info(`[Connection] Sending AI request ${requestId}:`, options)

    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error('AI request timeout'))
      }, 60000) // 60秒超时

      this.pendingRequests.set(requestId, { resolve, reject, timeout })

      this.port!.postMessage({
        type: 'aiRequest',
        data: options,
        requestId
      })
    })
  }

  /**
   * 注册消息处理器
   */
  onMessage(type: string, handler: (data: any) => void): void {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   */
  offMessage(type: string): void {
    this.messageHandlers.delete(type)
  }

  /**
   * 获取连接状态
   */
  getState(): ConnectionState {
    return this.state
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.port) {
      this.port.disconnect()
      this.port = null
    }
    this.state = ConnectionState.DISCONNECTED
    this.connectionPromise = null
  }
}

/**
 * Background端连接管理器
 * 负责处理来自popup的长连接和AI请求
 */
export class BackgroundConnectionManager {
  private static instance: BackgroundConnectionManager | null = null
  private connections = new Map<string, chrome.runtime.Port>()
  private aiRequestHandler: ((request: any) => Promise<any>) | null = null

  private constructor() {}

  static getInstance(): BackgroundConnectionManager {
    if (!BackgroundConnectionManager.instance) {
      BackgroundConnectionManager.instance = new BackgroundConnectionManager()
    }
    return BackgroundConnectionManager.instance
  }

  /**
   * 初始化连接管理器
   */
  init(aiRequestHandler: (request: any) => Promise<any>): void {
    this.aiRequestHandler = aiRequestHandler

    chrome.runtime.onConnect.addListener((port) => {
      if (port.name === 'popup-ai-connection') {
        this.handleNewConnection(port)
      }
    })

    Logger.info('[BackgroundConnection] Connection manager initialized')
  }

  /**
   * 处理新连接
   */
  private handleNewConnection(port: chrome.runtime.Port): void {
    const connectionId = `${port.name}-${Date.now()}`
    this.connections.set(connectionId, port)

    Logger.info(`[BackgroundConnection] New connection established: ${connectionId}`)

    port.onMessage.addListener(async (message) => {
      await this.handleMessage(port, message)
    })

    port.onDisconnect.addListener(() => {
      Logger.info(`[BackgroundConnection] Connection disconnected: ${connectionId}`)
      this.connections.delete(connectionId)
    })
  }

  /**
   * 处理接收到的消息
   */
  private async handleMessage(port: chrome.runtime.Port, message: any): Promise<void> {
    try {
      Logger.debug('[BackgroundConnection] Received message:', message)

      switch (message.type) {
        case 'connection-established':
          // 连接确认，发送确认响应
          port.postMessage({
            type: 'connection-confirmed',
            timestamp: Date.now()
          })
          break

        case 'aiRequest':
          await this.handleAIRequest(port, message)
          break

        default:
          Logger.warn('[BackgroundConnection] Unknown message type:', message.type)
          if (message.requestId) {
            port.postMessage({
              requestId: message.requestId,
              error: `Unknown message type: ${message.type}`
            })
          }
      }
    } catch (error) {
      Logger.error('[BackgroundConnection] Error handling message:', error)
      if (message.requestId) {
        port.postMessage({
          requestId: message.requestId,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  }

  /**
   * 处理AI请求
   */
  private async handleAIRequest(port: chrome.runtime.Port, message: any): Promise<void> {
    if (!this.aiRequestHandler) {
      throw new Error('AI request handler not initialized')
    }

    const { requestId, data } = message
    Logger.info(`[BackgroundConnection] Processing AI request ${requestId}`)

    try {
      // 构造与原有handleAiRequest兼容的请求格式
      const aiRequest = {
        type: 'aiRequest',
        prompt: data.prompt,
        options: {
          mode: data.mode,
          projectId: data.projectId,
          language: data.language,
          description: data.description,
          formFields: data.formFields
        }
      }

      const response = await this.aiRequestHandler(aiRequest)

      port.postMessage({
        requestId,
        data: response
      })

      Logger.success(`[BackgroundConnection] AI request ${requestId} completed successfully`)
    } catch (error) {
      Logger.error(`[BackgroundConnection] AI request ${requestId} failed:`, error)
      port.postMessage({
        requestId,
        error: error instanceof Error ? error.message : 'AI request failed'
      })
    }
  }

  /**
   * 广播消息到所有连接
   */
  broadcast(type: string, data: any): void {
    this.connections.forEach((port, connectionId) => {
      try {
        port.postMessage({ type, data })
        Logger.debug(`[BackgroundConnection] Broadcasted ${type} to ${connectionId}`)
      } catch (error) {
        Logger.error(`[BackgroundConnection] Failed to broadcast to ${connectionId}:`, error)
        // 移除失效的连接
        this.connections.delete(connectionId)
      }
    })
  }

  /**
   * 获取活跃连接数
   */
  getActiveConnectionCount(): number {
    return this.connections.size
  }
}
