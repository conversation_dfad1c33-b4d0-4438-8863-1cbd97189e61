/**
 * 简单的数据混淆工具
 * 用于在popup和background之间传输时混淆敏感数据
 */

/**
 * 简单的字符串编码（Base64 + 简单变换）
 */
export function encodeData(data: any): string {
  try {
    const jsonString = JSON.stringify(data)
    const base64 = btoa(jsonString)
    // 简单的字符替换混淆
    return base64.split('').reverse().join('')
  } catch (error) {
    throw new Error('Failed to encode data')
  }
}

/**
 * 解码数据
 */
export function decodeData(encodedData: string): any {
  try {
    // 反向字符替换
    const base64 = encodedData.split('').reverse().join('')
    const jsonString = atob(base64)
    return JSON.parse(jsonString)
  } catch (error) {
    throw new Error('Failed to decode data')
  }
}

/**
 * 生成请求ID的哈希值，避免暴露真实的requestId模式
 */
export function hashRequestId(requestId: number): string {
  return btoa(requestId.toString()).replace(/[+=]/g, '').substring(0, 8)
}
