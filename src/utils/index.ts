// 日志工具
export const Logger = {
  info: (message: string, data: any = null) => {
    console.log(
      '%c[Formify Info]%c ' + message,
      'color: #2196F3; font-weight: bold',
      'color: inherit',
      data ? data : ''
    );
  },
  success: (message: string, data: any = null) => {
    console.log(
      '%c[Formify Success]%c ' + message,
      'color: #4CAF50; font-weight: bold',
      'color: inherit',
      data ? data : ''
    );
  },
  error: (message: string, error: any = null) => {
    console.error(
      '%c[Formify Error]%c ' + message,
      'color: #f44336; font-weight: bold',
      'color: inherit',
      error ? error : ''
    );
  },
  warn: (message: string, data: any = null) => {
    console.warn(
      '%c[Formify Warning]%c ' + message,
      'color: #FF9800; font-weight: bold',
      'color: inherit',
      data ? data : ''
    );
  },
  debug: (message: string, data: any = null) => {
    console.log(
      '%c[Formify Debug]%c ' + message,
      'color: #9E9E9E; font-weight: bold',
      'color: inherit',
      data ? data : ''
    );
  },
  api: {
    request: (provider: string, endpoint: string, data: any) => {
      console.log(
        `%c[Formify API Request]%c\nProvider: %c${provider}%c\nEndpoint: %c${endpoint}%c\nPayload:`,
        'color: #9C27B0; font-weight: bold',
        'color: inherit',
        'color: #1976D2; font-weight: bold',
        'color: inherit',
        'color: #1976D2; font-weight: bold',
        'color: inherit',
        data
      );
    },
    response: (provider: string, response: any) => {
      console.log(
        `%c[Formify API Response]%c\nProvider: %c${provider}%c\nResponse:`,
        'color: #9C27B0; font-weight: bold',
        'color: inherit',
        'color: #1976D2; font-weight: bold',
        'color: inherit',
        response
      );
    }
  }
};

// 存储工具
export const Storage = {
  // 获取设置
  getSettings: async () => {
    try {
      const result = await chrome.storage.sync.get(['formify_settings']);
      return result.formify_settings || {};
    } catch (error) {
      Logger.error('Error getting settings', error);
      return {};
    }
  },
  
  // 保存设置
  saveSettings: async (settings: any) => {
    try {
      await chrome.storage.sync.set({ formify_settings: settings });
      return true;
    } catch (error) {
      Logger.error('Error saving settings', error);
      return false;
    }
  },
  
  // 获取 API 密钥
  getApiKeys: async () => {
    try {
      const result = await chrome.storage.sync.get(['formify_api_keys']);
      return result.formify_api_keys || {};
    } catch (error) {
      Logger.error('Error getting API keys', error);
      return {};
    }
  },
  
  // 保存 API 密钥
  saveApiKey: async (provider: string, key: string) => {
    try {
      const apiKeys = await Storage.getApiKeys();
      apiKeys[provider] = key;
      await chrome.storage.sync.set({ formify_api_keys: apiKeys });
      return true;
    } catch (error) {
      Logger.error('Error saving API key', error);
      return false;
    }
  },
  
  // 获取项目
  getProjects: async () => {
    try {
      const result = await chrome.storage.sync.get(['formify_projects']);
      return result.formify_projects || [];
    } catch (error) {
      Logger.error('Error getting projects', error);
      return [];
    }
  },
  
  // 保存项目
  saveProject: async (project: any) => {
    try {
      const projects = await Storage.getProjects();
      const index = projects.findIndex((p: any) => p.id === project.id);
      
      if (index !== -1) {
        projects[index] = project;
      } else {
        projects.push(project);
      }
      
      await chrome.storage.sync.set({ formify_projects: projects });
      return true;
    } catch (error) {
      Logger.error('Error saving project', error);
      return false;
    }
  },
  
  // 删除项目
  deleteProject: async (projectId: string) => {
    try {
      const projects = await Storage.getProjects();
      const filteredProjects = projects.filter((p: any) => p.id !== projectId);
      await chrome.storage.sync.set({ formify_projects: filteredProjects });
      return true;
    } catch (error) {
      Logger.error('Error deleting project', error);
      return false;
    }
  },
  
  // 获取自定义提供商
  getCustomProviders: async () => {
    try {
      const result = await chrome.storage.sync.get(['formify_custom_providers']);
      return result.formify_custom_providers || {};
    } catch (error) {
      Logger.error('Error getting custom providers', error);
      return {};
    }
  },
  
  // 保存自定义提供商
  saveCustomProvider: async (name: string, provider: any) => {
    try {
      const customProviders = await Storage.getCustomProviders();
      customProviders[name] = provider;
      await chrome.storage.sync.set({ formify_custom_providers: customProviders });
      return true;
    } catch (error) {
      Logger.error('Error saving custom provider', error);
      return false;
    }
  },
  
  // 删除自定义提供商
  deleteCustomProvider: async (name: string) => {
    try {
      const customProviders = await Storage.getCustomProviders();
      delete customProviders[name];
      await chrome.storage.sync.set({ formify_custom_providers: customProviders });
      return true;
    } catch (error) {
      Logger.error('Error deleting custom provider', error);
      return false;
    }
  }
};

// 生成唯一 ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 格式化日期
export const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

// 深度合并对象
export const deepMerge = (target: any, source: any) => {
  const output = Object.assign({}, target);
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          Object.assign(output, { [key]: source[key] });
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  
  return output;
};

// 检查是否为对象
const isObject = (item: any) => {
  return (item && typeof item === 'object' && !Array.isArray(item));
};

// 防抖函数
export const debounce = (func: Function, wait: number) => {
  let timeout: number | null = null;
  
  return (...args: any[]) => {
    if (timeout) clearTimeout(timeout);
    timeout = window.setTimeout(() => {
      func(...args);
    }, wait);
  };
};

// 节流函数
export const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  
  return (...args: any[]) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 获取浏览器语言
export const getBrowserLanguage = () => {
  return navigator.language.split('-')[0];
};

// 检查 URL 是否有效
export const isValidUrl = (url: string) => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

// 获取当前标签页 URL
export const getCurrentTabUrl = async () => {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    return tabs[0]?.url || '';
  } catch (error) {
    Logger.error('Error getting current tab URL', error);
    return '';
  }
};

// 发送消息到内容脚本
export const sendMessageToContentScript = async (message: any) => {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs[0]?.id) {
      return await chrome.tabs.sendMessage(tabs[0].id, message);
    }
    throw new Error('No active tab found');
  } catch (error) {
    Logger.error('Error sending message to content script', error);
    throw error;
  }
};

// 发送消息到后台脚本
export const sendMessageToBackground = async (message: any) => {
  try {
    return await chrome.runtime.sendMessage(message);
  } catch (error) {
    Logger.error('Error sending message to background', error);
    throw error;
  }
};

// 检查 API 密钥是否有效
export const validateApiKey = async (provider: string, apiKey: string) => {
  try {
    const response = await sendMessageToBackground({
      action: 'validateApiKey',
      data: { provider, apiKey }
    });
    
    return response.success;
  } catch (error) {
    Logger.error('Error validating API key', error);
    return false;
  }
};

// 获取表单填充提示
export const getFormFillPrompt = (mode: string, formData: any, language: string = 'en') => {
  // 基础提示
  let basePrompt = '';
  
  // 根据语言选择提示语言
  if (language === 'zh') {
    basePrompt = '你是一个表单填写助手。请根据以下表单字段，生成合适的填写内容。';
  } else {
    basePrompt = 'You are a form filling assistant. Please generate appropriate content for the following form fields.';
  }
  
  // 根据模式添加特定提示
  switch (mode) {
    case 'email':
      if (language === 'zh') {
        basePrompt += '这是一封电子邮件。请生成专业、礼貌的内容。';
      } else {
        basePrompt += ' This is an email. Please generate professional and polite content.';
      }
      break;
    case 'bug_report':
      if (language === 'zh') {
        basePrompt += '这是一份错误报告。请生成详细、具体的内容，包括错误描述、复现步骤和预期行为。';
      } else {
        basePrompt += ' This is a bug report. Please generate detailed and specific content, including error description, reproduction steps, and expected behavior.';
      }
      break;
    default:
      break;
  }
  
  // 添加表单数据
  if (language === 'zh') {
    basePrompt += '\n\n表单字段：\n';
  } else {
    basePrompt += '\n\nForm fields:\n';
  }
  
  // 添加表单字段
  for (const field of formData) {
    const label = field.label || field.placeholder || field.name || field.id || 'Unnamed field';
    basePrompt += `- ${label}\n`;
  }
  
  // 添加输出格式说明
  if (language === 'zh') {
    basePrompt += '\n请以 JSON 格式返回结果，字段名为表单字段名，值为生成的内容。例如：\n';
    basePrompt += '{\n  "字段1": "内容1",\n  "字段2": "内容2"\n}';
  } else {
    basePrompt += '\nPlease return the result in JSON format, with field names as keys and generated content as values. For example:\n';
    basePrompt += '{\n  "Field1": "Content1",\n  "Field2": "Content2"\n}';
  }
  
  return basePrompt;
};
