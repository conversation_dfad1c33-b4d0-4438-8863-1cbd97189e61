# AI请求通信升级 - 长连接方案

## 概述

成功将popup和background之间的AI请求通信从一次性消息传递升级为长连接（chrome.runtime.connect）+ 独立异步响应方案。

## 改造内容

### 1. 新增连接管理器 (`src/utils/connection-manager.ts`)

#### PopupConnectionManager (Popup端)
- **持久连接管理**: 建立并维护与background的长连接
- **自动重连机制**: 连接断开时自动重连，支持指数退避策略
- **请求-响应映射**: 通过requestId管理异步请求和响应
- **超时处理**: 60秒AI请求超时保护
- **状态管理**: 实时跟踪连接状态

#### BackgroundConnectionManager (Background端)
- **连接池管理**: 管理多个popup连接
- **消息路由**: 将AI请求路由到现有的handleAiRequest函数
- **广播功能**: 支持向所有连接广播消息
- **错误处理**: 统一的错误处理和响应机制

### 2. Background Script集成 (`entrypoints/background.ts`)

```typescript
// 初始化连接管理器
const connectionManager = BackgroundConnectionManager.getInstance()
connectionManager.init(handleAiRequest)
```

- 在background script启动时初始化连接管理器
- 将现有的`handleAiRequest`函数作为AI请求处理器传入
- 保持与现有代码的完全兼容性

### 3. Popup组件升级 (`entrypoints/popup/App.vue`)

#### 连接管理
```typescript
// 连接管理器
const connectionManager = PopupConnectionManager.getInstance()
const connectionState = ref<ConnectionState>(ConnectionState.DISCONNECTED)

// 初始化连接
await connectionManager.connect()
```

#### AI请求调用
```typescript
// 旧方式 (一次性消息)
const aiResponse = await chrome.runtime.sendMessage({
  type: 'aiRequest',
  prompt: description.value,
  options: { ... }
})

// 新方式 (长连接)
const aiResponse = await connectionManager.sendAIRequest({
  prompt: description.value,
  mode: currentMode.value,
  projectId: selectedProject.value,
  language: selectedLanguage.value,
  description: description.value,
  formFields: formFieldsResponse.fields
})
```

## 技术优势

### 1. 连接稳定性
- ✅ 持久连接，避免频繁建立/断开
- ✅ 自动重连机制，提高可靠性
- ✅ 减少"Receiving end does not exist"错误

### 2. 异步响应支持
- ✅ 支持独立的异步响应，不阻塞消息处理
- ✅ 60秒超时保护，适合长时间AI请求
- ✅ 支持未来的流式响应和进度更新

### 3. 双向通信
- ✅ Background可以主动向popup推送状态更新
- ✅ 实时同步登录状态、AI处理进度等
- ✅ 支持广播消息到多个连接

### 4. 更好的错误处理
- ✅ 连接断开事件可以被监听和处理
- ✅ 自动重连逻辑更简洁
- ✅ 统一的错误处理机制

## 兼容性保证

- ✅ 保持现有`handleAiRequest`函数不变
- ✅ AI请求的数据结构完全兼容
- ✅ 其他消息通信（登录状态等）保持原有方式
- ✅ 渐进式升级，可以逐步迁移其他通信

## 使用方式

### 发送AI请求
```typescript
const connectionManager = PopupConnectionManager.getInstance()

try {
  const response = await connectionManager.sendAIRequest({
    prompt: "用户描述",
    mode: "email",
    language: "auto",
    description: "详细描述",
    formFields: [...] // 表单字段数组
  })
  
  if (response.success) {
    // 处理成功响应
    console.log('AI生成内容:', response.data)
  } else {
    // 处理错误
    console.error('AI请求失败:', response.error)
  }
} catch (error) {
  // 处理连接或超时错误
  console.error('连接错误:', error.message)
}
```

### 监听连接状态
```typescript
connectionManager.onMessage('connectionStateChanged', (state) => {
  console.log('连接状态变化:', state)
})
```

## 测试验证

- ✅ 构建成功，无编译错误
- ✅ 保持现有功能完整性
- ✅ 连接管理器正确初始化
- ✅ AI请求数据结构兼容

## 后续计划

1. **性能监控**: 添加连接性能和请求延迟监控
2. **流式响应**: 支持AI请求的流式响应和实时进度
3. **其他通信迁移**: 逐步将其他消息通信迁移到长连接
4. **连接池优化**: 优化连接池管理和资源使用

## 总结

成功完成AI请求通信的长连接升级，显著提升了通信的稳定性和可靠性，为未来的功能扩展奠定了坚实基础。改造过程保持了完全的向后兼容性，确保现有功能不受影响。
