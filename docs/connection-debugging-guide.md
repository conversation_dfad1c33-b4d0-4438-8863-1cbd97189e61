# 连接管理器调试指南 & AI请求隐私保护

## 🔒 AI请求隐私保护措施

### 实现的隐私保护功能

我们实现了多层隐私保护，确保AI请求内容不会在popup devtools中暴露：

#### 1. **数据编码混淆**
- 使用Base64编码 + 字符反转的方式混淆敏感数据
- AI请求的prompt、formFields等敏感信息在传输时被编码
- 只有background script能解码真实内容

#### 2. **请求ID哈希化**
- 真实的requestId被哈希化，避免暴露请求模式
- 使用8位哈希ID代替递增的数字ID
- 维护哈希ID到真实ID的内部映射

#### 3. **敏感日志移除**
- 移除所有会暴露AI请求内容的日志
- 只记录请求状态，不记录具体内容
- 使用哈希ID进行日志记录

### 隐私保护前后对比

#### 🚫 **修复前 - 数据暴露**
```javascript
// popup devtools中可见的内容
Logger.info('Sending AI request:', {
  prompt: "请帮我填写这个表单...",
  formFields: [...],
  // 其他敏感信息
})

chrome.runtime.Port.postMessage({
  type: 'aiRequest',
  data: {
    prompt: "请帮我填写这个表单...", // 明文可见
    formFields: [...] // 表单数据可见
  },
  requestId: 123 // 可预测的ID
})
```

#### ✅ **修复后 - 数据保护**
```javascript
// popup devtools中只能看到
Logger.info('Sending AI request a7b9c2d1') // 哈希ID

chrome.runtime.Port.postMessage({
  type: 'aiRequest',
  payload: 'mR2bvN1c...', // 编码后的混淆数据
  requestId: 'a7b9c2d1' // 哈希ID
})
```

### 技术实现细节

#### 数据编码流程
```typescript
// 1. popup端编码
const options = { prompt: "敏感内容", formFields: [...] }
const encodedData = encodeData(options) // "mR2bvN1c..."

// 2. background端解码
const realData = decodeData(encodedData) // 恢复原始数据
```

#### 请求ID映射
```typescript
// popup端
const requestId = 123
const hashedId = hashRequestId(requestId) // "a7b9c2d1"
this.requestIdMapping.set(hashedId, requestId)

// 响应时通过哈希ID找回真实ID
const realId = this.requestIdMapping.get(hashedId)
```

## 问题修复总结

### 已修复的问题

1. **Logger方法缺失错误**
   - **错误**: `TypeError: T.debug is not a function`, `TypeError: M.debug is not a function`
   - **原因**: Logger对象缺少`debug`和`warn`方法
   - **修复**: 在`src/utils/index.ts`和`entrypoints/background.ts`中添加了完整的Logger方法

2. **连接确认机制**
   - **问题**: 连接状态管理不准确
   - **修复**: 添加了连接确认机制，只有在收到background确认后才设置为CONNECTED状态

3. **日志启用**
   - **问题**: 生产模式下日志被禁用，难以调试
   - **修复**: 临时启用了background script中的所有日志方法

### 修复后的Logger接口

```typescript
// src/utils/index.ts
export const Logger = {
  info: (message: string, data: any = null) => { ... },
  success: (message: string, data: any = null) => { ... },
  error: (message: string, error: any = null) => { ... },
  warn: (message: string, data: any = null) => { ... },    // 新增
  debug: (message: string, data: any = null) => { ... }    // 新增
}

// entrypoints/background.ts
const Logger: Logger = {
  info: (...args) => console.log('[Formify Info]', ...args),
  error: (...args) => console.error('[Formify Error]', ...args),
  success: (...args) => console.log('[Formify Success]', ...args),
  warn: (...args) => console.warn('[Formify Warning]', ...args),
  debug: (...args) => console.log('[Formify Debug]', ...args)
}
```

## 调试步骤

### 1. 检查连接状态

在浏览器开发者工具中查看以下日志：

**Popup端 (popup控制台)**:
```
[Formify Info] [Connection] Establishing connection to background...
[Formify Success] [Connection] Successfully connected to background
```

**Background端 (扩展页面控制台)**:
```
[Formify Info] [BackgroundConnection] Connection manager initialized
[Formify Info] [BackgroundConnection] New connection established: popup-ai-connection-[timestamp]
```

### 2. 检查AI请求流程

**发送请求时的日志**:
```
[Formify Info] [Connection] Sending AI request [requestId]: {...}
[Formify Info] [BackgroundConnection] Processing AI request [requestId]
[Formify Info] Handling AI request: {...}
```

**成功响应时的日志**:
```
[Formify Success] AI request successful: {...}
[Formify Success] [BackgroundConnection] AI request [requestId] completed successfully
[Formify Info] [Connection] Request [requestId] succeeded: {...}
```

**错误响应时的日志**:
```
[Formify Error] Error in AI request: {...}
[Formify Error] [BackgroundConnection] AI request [requestId] failed: {...}
[Formify Error] [Connection] Request [requestId] failed: {...}
```

### 3. 常见问题排查

#### 问题1: "AI request timeout"
- **可能原因**: AI请求超过60秒超时限制
- **检查**: 查看background控制台是否有AI provider相关错误
- **解决**: 检查API密钥是否有效，网络是否正常

#### 问题2: "Connection not available"
- **可能原因**: 连接未建立或已断开
- **检查**: 查看连接建立日志
- **解决**: 刷新popup或重新加载扩展

#### 问题3: "No validated API key for [provider]"
- **可能原因**: API密钥未验证或不存在
- **检查**: 在设置页面验证API密钥
- **解决**: 重新输入并验证API密钥

### 4. 调试工具

#### 在popup中添加连接状态显示
```vue
<template>
  <div>
    <p>连接状态: {{ connectionState }}</p>
    <!-- 其他内容 -->
  </div>
</template>
```

#### 手动测试连接
```javascript
// 在popup控制台中执行
const manager = PopupConnectionManager.getInstance()
console.log('连接状态:', manager.getState())

// 测试AI请求
manager.sendAIRequest({
  prompt: "测试",
  mode: "email",
  language: "auto",
  description: "测试描述",
  formFields: []
}).then(response => {
  console.log('AI响应:', response)
}).catch(error => {
  console.error('AI请求失败:', error)
})
```

## 性能监控

### 连接性能指标
- 连接建立时间
- 重连次数
- 请求响应时间
- 超时次数

### 添加性能监控代码
```typescript
// 在connection-manager中添加
private connectionStartTime: number = 0
private requestStartTimes: Map<number, number> = new Map()

// 连接开始时
this.connectionStartTime = Date.now()

// 连接成功时
const connectionTime = Date.now() - this.connectionStartTime
Logger.info(`[Connection] Connection established in ${connectionTime}ms`)

// 请求开始时
this.requestStartTimes.set(requestId, Date.now())

// 请求完成时
const requestTime = Date.now() - this.requestStartTimes.get(requestId)!
Logger.info(`[Connection] Request ${requestId} completed in ${requestTime}ms`)
```

## 恢复生产模式

调试完成后，记得恢复生产模式的日志设置：

```typescript
// entrypoints/background.ts
const Logger: Logger = {
  info: (..._args) => {/* Production mode - logging disabled */},
  error: (...args) => console.error('[Formify Error]', ...args),
  success: (..._args) => {/* Production mode - logging disabled */},
  warn: (..._args) => {/* Production mode - logging disabled */},
  debug: (..._args) => {/* Production mode - logging disabled */}
}
```

## 总结

通过以上修复，应该解决了：
1. ✅ Logger方法缺失导致的TypeError
2. ✅ 连接状态管理问题
3. ✅ 调试信息不足的问题

如果仍有问题，请按照调试步骤逐步排查，重点关注：
- 连接是否成功建立
- AI请求数据格式是否正确
- API密钥是否有效
- 网络请求是否正常
